#ifndef VIDEO_ENCODER_H
#define VIDEO_ENCODER_H

#include "common.h"
#include <memory>
#include <atomic>
#include <mutex>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

#ifdef USE_FFMPEG
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}
#endif

/**
 * @brief 视频编码配置
 */
struct VideoEncoderConfig {
    // 基本参数
    std::string codec = "h264";          // 编码器类型
    int width = 640;                     // 视频宽度
    int height = 480;                    // 视频高度
    int fps = 15;                        // 帧率
    int bitrate = 2000000;              // 码率 (bps)
    
    // 质量参数
    std::string preset = "fast";         // 编码预设: ultrafast, fast, medium, slow
    std::string profile = "baseline";    // 编码配置: baseline, main, high
    int crf = 23;                       // 恒定质量因子 (0-51, 越小质量越好)
    int keyFrameInterval = 30;          // 关键帧间隔
    
    // 性能参数
    int threads = 2;                    // 编码线程数
    bool enableHardwareAccel = false;   // 硬件加速
    std::string hwAccelDevice = "auto"; // 硬件加速设备
    
    // 缓冲参数
    int bufferSize = 1024 * 1024;      // 输出缓冲区大小
    int maxBFrames = 0;                // B帧数量
    
    // 像素格式
    std::string pixelFormat = "yuv420p"; // 像素格式
};

/**
 * @brief 编码后的数据包
 */
struct EncodedPacket {
    std::unique_ptr<uint8_t[]> data;    // 编码数据
    size_t size = 0;                    // 数据大小
    int64_t pts = 0;                    // 显示时间戳
    int64_t dts = 0;                    // 解码时间戳
    bool isKeyFrame = false;            // 是否为关键帧
    
    EncodedPacket() = default;
    EncodedPacket(const EncodedPacket&) = delete;
    EncodedPacket& operator=(const EncodedPacket&) = delete;
    
    EncodedPacket(EncodedPacket&& other) noexcept
        : data(std::move(other.data))
        , size(other.size)
        , pts(other.pts)
        , dts(other.dts)
        , isKeyFrame(other.isKeyFrame) {
        other.size = 0;
        other.pts = 0;
        other.dts = 0;
        other.isKeyFrame = false;
    }
    
    EncodedPacket& operator=(EncodedPacket&& other) noexcept {
        if (this != &other) {
            data = std::move(other.data);
            size = other.size;
            pts = other.pts;
            dts = other.dts;
            isKeyFrame = other.isKeyFrame;
            
            other.size = 0;
            other.pts = 0;
            other.dts = 0;
            other.isKeyFrame = false;
        }
        return *this;
    }
};

/**
 * @brief 视频编码器状态
 */
enum class EncoderStatus {
    UNINITIALIZED,  // 未初始化
    INITIALIZED,    // 已初始化
    ENCODING,       // 编码中
    ERROR          // 错误状态
};

/**
 * @brief FFmpeg视频编码器
 * 
 * 使用FFmpeg库实现H.264视频编码，支持硬件加速和实时编码
 */
class VideoEncoder {
public:
    VideoEncoder();
    ~VideoEncoder();
    
    // 禁用拷贝构造和赋值
    VideoEncoder(const VideoEncoder&) = delete;
    VideoEncoder& operator=(const VideoEncoder&) = delete;
    
    /**
     * @brief 初始化编码器
     * @param config 编码配置
     * @return 是否初始化成功
     */
    bool initialize(const VideoEncoderConfig& config);
    
    /**
     * @brief 编码视频帧
     * @param frame OpenCV图像帧
     * @param packet 输出编码数据包
     * @return 是否编码成功
     */
    bool encode(const cv::Mat& frame, EncodedPacket& packet);
    
    /**
     * @brief 刷新编码器缓冲区
     * @param packets 输出剩余的编码数据包
     * @return 是否刷新成功
     */
    bool flush(std::vector<EncodedPacket>& packets);
    
    /**
     * @brief 获取编码器状态
     * @return 当前状态
     */
    EncoderStatus getStatus() const { return status_; }
    
    /**
     * @brief 获取编码配置
     * @return 当前配置
     */
    const VideoEncoderConfig& getConfig() const { return config_; }
    
    /**
     * @brief 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return status_ != EncoderStatus::UNINITIALIZED; }
    
    /**
     * @brief 获取编码器信息
     * @return 编码器信息字符串
     */
    std::string getEncoderInfo() const;
    
    /**
     * @brief 重置编码器
     */
    void reset();

    /**
     * @brief 动态调整码率
     * @param newBitrate 新的码率 (bps)
     * @return 是否调整成功
     */
    bool adjustBitrate(int newBitrate);

    /**
     * @brief 获取当前实际码率
     * @return 当前码率 (bps)
     */
    int getCurrentBitrate() const;

private:
#ifdef USE_FFMPEG
    // FFmpeg组件
    const AVCodec* codec_ = nullptr;            // 编码器
    AVCodecContext* codecContext_ = nullptr;    // 编码器上下文
    AVFrame* frame_ = nullptr;                  // 输入帧
    AVPacket* packet_ = nullptr;                // 输出包
    SwsContext* swsContext_ = nullptr;          // 图像转换上下文
    
    // 缓冲区
    uint8_t* frameBuffer_ = nullptr;            // 帧缓冲区
    int frameBufferSize_ = 0;                   // 缓冲区大小
#endif
    
    // 配置和状态
    VideoEncoderConfig config_;                 // 编码配置
    std::atomic<EncoderStatus> status_;         // 编码器状态
    
    // 时间戳管理
    int64_t frameCount_ = 0;                    // 帧计数
    int64_t timeBase_ = 0;                      // 时间基准
    
    // 线程安全
    mutable std::mutex encoderMutex_;           // 编码器互斥锁
    
    /**
     * @brief 初始化FFmpeg编码器
     * @return 是否初始化成功
     */
    bool initializeFFmpeg();
    
    /**
     * @brief 配置编码器参数
     * @return 是否配置成功
     */
    bool configureEncoder();
    
    /**
     * @brief 初始化图像转换器
     * @return 是否初始化成功
     */
    bool initializeScaler();
    
    /**
     * @brief 转换OpenCV图像到FFmpeg格式
     * @param cvFrame OpenCV图像
     * @param avFrame FFmpeg帧
     * @return 是否转换成功
     */
    bool convertFrame(const cv::Mat& cvFrame, AVFrame* avFrame);
    
    /**
     * @brief 清理FFmpeg资源
     */
    void cleanupFFmpeg();
    
    /**
     * @brief 记录错误信息
     * @param message 错误消息
     * @param errorCode FFmpeg错误码
     */
    void logError(const std::string& message, int errorCode = 0) const;
    
    /**
     * @brief 记录信息
     * @param message 信息消息
     */
    void logInfo(const std::string& message) const;
    
    /**
     * @brief 获取FFmpeg错误字符串
     * @param errorCode 错误码
     * @return 错误描述
     */
    std::string getFFmpegError(int errorCode) const;
};

#endif // VIDEO_ENCODER_H
