cmake_minimum_required(VERSION 3.10)
project(FaultDetectRefactored)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3 /MP")  # /MP 启用并行编译
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
    # 在Release模式下启用更多优化
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -march=native")
    endif()
endif()

# 查找OpenCV (可选)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
    add_definitions(-DUSE_OPENCV)
else()
    message(WARNING "OpenCV not found! Building without OpenCV support.")
    message(STATUS "Please install OpenCV to enable full functionality.")
endif()

# 查找线程库
find_package(Threads REQUIRED)

# 查找SQLite3库
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(SQLITE3 QUIET sqlite3)
endif()

if(NOT SQLITE3_FOUND)
    find_path(SQLITE3_INCLUDE_DIR sqlite3.h)
    find_library(SQLITE3_LIBRARY sqlite3)
    if(SQLITE3_INCLUDE_DIR AND SQLITE3_LIBRARY)
        set(SQLITE3_FOUND TRUE)
        set(SQLITE3_INCLUDE_DIRS ${SQLITE3_INCLUDE_DIR})
        set(SQLITE3_LIBRARIES ${SQLITE3_LIBRARY})
        message(STATUS "SQLite3 found: ${SQLITE3_LIBRARY}")
    else()
        message(FATAL_ERROR "SQLite3 not found! Please install SQLite3 development package.")
    endif()
else()
    message(STATUS "SQLite3 found via pkg-config: ${SQLITE3_LIBRARIES}")
endif()

# 查找FFmpeg (用于RTSP推流)
find_path(AVCODEC_INCLUDE_DIR libavcodec/avcodec.h)
find_library(AVCODEC_LIBRARY avcodec)
find_path(AVFORMAT_INCLUDE_DIR libavformat/avformat.h)
find_library(AVFORMAT_LIBRARY avformat)
find_path(AVUTIL_INCLUDE_DIR libavutil/avutil.h)
find_library(AVUTIL_LIBRARY avutil)
find_path(SWSCALE_INCLUDE_DIR libswscale/swscale.h)
find_library(SWSCALE_LIBRARY swscale)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

if(SQLITE3_FOUND)
    include_directories(${SQLITE3_INCLUDE_DIRS})
endif()

if(AVCODEC_INCLUDE_DIR)
    include_directories(${AVCODEC_INCLUDE_DIR})
    include_directories(${AVFORMAT_INCLUDE_DIR})
    include_directories(${AVUTIL_INCLUDE_DIR})
    include_directories(${SWSCALE_INCLUDE_DIR})
endif()

# 源文件
if(OpenCV_FOUND)
    # 使用重构后的完整版本（默认）
    set(SOURCES
        "src/common.cpp"
        "src/config_manager.cpp"
        "src/main_refactored.cpp"
        "src/camera/camera_manager.cpp"
        "src/damage_detection_engine.cpp"
        "src/database_manager.cpp"
        "src/test/test_manager.cpp"
        "src/test/test_streaming.cpp"
        "src/rtsp/video_encoder.cpp"
        "src/rtsp/stream_client.cpp"
        "src/rtsp/rtsp_stream_manager.cpp"
    )
    message(STATUS "Building with refactored main program (main_refactored.cpp)")
else()
    # 使用简化版本
    set(SOURCES
        "src/common_simple.cpp"
        "src/test_simple.cpp"
    )
    message(STATUS "Building simplified version without OpenCV")
endif()

# 头文件
file(GLOB_RECURSE HEADERS
    "include/*.h"
    "include/*.hpp"
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Threads::Threads
)

if(OpenCV_FOUND)
    target_link_libraries(${PROJECT_NAME} ${OpenCV_LIBS})
endif()

# 链接SQLite3库
if(SQLITE3_FOUND)
    target_link_libraries(${PROJECT_NAME} ${SQLITE3_LIBRARIES})
endif()

# 如果找到FFmpeg，则链接FFmpeg库
if(AVCODEC_LIBRARY AND AVFORMAT_LIBRARY AND AVUTIL_LIBRARY AND SWSCALE_LIBRARY)
    target_link_libraries(${PROJECT_NAME}
        ${AVCODEC_LIBRARY}
        ${AVFORMAT_LIBRARY}
        ${AVUTIL_LIBRARY}
        ${SWSCALE_LIBRARY}
    )
    add_definitions(-DUSE_FFMPEG)
    message(STATUS "FFmpeg found and will be used for RTSP streaming")
else()
    message(WARNING "FFmpeg not found. RTSP streaming will be disabled.")
endif()

# 在Windows下链接额外的库
if(WIN32)
    target_link_libraries(${PROJECT_NAME} ws2_32 winmm)
endif()

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 复制配置文件到构建目录
file(COPY ${CMAKE_SOURCE_DIR}/config/ DESTINATION ${CMAKE_BINARY_DIR}/config/)

# 创建数据输出目录
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/output)

# 编译选项
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG)
    if(NOT MSVC)
        set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    endif()
    message(STATUS "Debug mode enabled")
    message(STATUS "Debug flags: ${CMAKE_CXX_FLAGS_DEBUG}")
else()
    add_definitions(-DNDEBUG)
    message(STATUS "Release mode enabled")
    message(STATUS "Release flags: ${CMAKE_CXX_FLAGS_RELEASE}")
endif()

# 显示构建信息
message(STATUS "=== 拉吊索缺损识别系统构建配置 ===")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")
if(OpenCV_FOUND)
    message(STATUS "Main program: main_refactored.cpp (重构版本)")
else()
    message(STATUS "Main program: simplified version (简化版本)")
endif()
message(STATUS "=======================================")
