#include "../../include/test/test_manager.h"
#include "../../include/database_manager.h"
#include "../../include/damage_detection_engine.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <random>

TestManager::TestManager() {
    resetTestResults();
}

TestManager::~TestManager() = default;

bool TestManager::runAllTests() {
    Utils::logInfo("=== 开始运行所有测试 ===");
    resetTestResults();
    
    bool allPassed = true;
    
    // 运行基础功能测试
    Utils::logInfo("--- 基础功能测试 ---");
    bool basicPassed = runBasicTests();
    allPassed &= basicPassed;
    
#ifdef USE_OPENCV
    // 运行摄像头功能测试
    Utils::logInfo("--- 摄像头功能测试 ---");
    bool cameraPassed = runCameraTests();
    allPassed &= cameraPassed;
    
    // 运行系统集成测试
    Utils::logInfo("--- 系统集成测试 ---");
    bool systemPassed = runSystemTests();
    allPassed &= systemPassed;
#endif

    // 运行数据库功能测试
    Utils::logInfo("--- 数据库功能测试 ---");
    bool databasePassed = runDatabaseTests();
    allPassed &= databasePassed;

    // 运行推流功能测试
    Utils::logInfo("--- 推流功能测试 ---");
    bool streamingPassed = runStreamingTests();
    allPassed &= streamingPassed;
    
    displayTestSummary();
    
    if (allPassed) {
        Utils::logInfo("=== 所有测试通过 ===");
    } else {
        Utils::logError("=== 部分测试失败 ===");
    }
    
    return allPassed;
}

bool TestManager::runBasicTests() {
    Utils::logInfo("开始基础功能测试...");
    
    bool allPassed = BasicFunctionTest::runAllTests();
    recordTestResult("基础功能测试", allPassed);
    
    return allPassed;
}

bool TestManager::runCameraTests() {
#ifdef USE_OPENCV
    Utils::logInfo("开始摄像头功能测试...");
    
    bool allPassed = CameraFunctionTest::runAllTests();
    recordTestResult("摄像头功能测试", allPassed);
    
    return allPassed;
#else
    Utils::logWarning("OpenCV未启用，跳过摄像头功能测试");
    return true;
#endif
}

bool TestManager::runSystemTests() {
#ifdef USE_OPENCV
    Utils::logInfo("开始系统集成测试...");
    
    bool allPassed = SystemIntegrationTest::runAllTests();
    recordTestResult("系统集成测试", allPassed);
    
    return allPassed;
#else
    Utils::logWarning("OpenCV未启用，跳过系统集成测试");
    return true;
#endif
}

bool TestManager::runDatabaseTests() {
    Utils::logInfo("开始数据库功能测试...");

    bool allPassed = DatabaseFunctionTest::runAllTests();
    recordTestResult("数据库功能测试", allPassed);

    return allPassed;
}

bool TestManager::runStreamingTests() {
    Utils::logInfo("开始推流功能测试...");

    // 声明外部测试函数
    extern bool runStreamingTestsImpl();

    // 调用外部测试函数
    bool allPassed = runStreamingTestsImpl();
    recordTestResult("推流功能测试", allPassed);

    return allPassed;
}

void TestManager::recordTestResult(const std::string& testName, bool passed) {
    testResults.totalTests++;
    if (passed) {
        testResults.passedTests++;
        Utils::logInfo("✓ " + testName + ": 通过");
    } else {
        testResults.failedTests++;
        testResults.failedTestNames.push_back(testName);
        Utils::logError("✗ " + testName + ": 失败");
    }
}

void TestManager::displayTestSummary() const {
    Utils::logInfo("=== 测试结果摘要 ===");
    Utils::logInfo("总测试数: " + std::to_string(testResults.totalTests));
    Utils::logInfo("通过测试: " + std::to_string(testResults.passedTests));
    Utils::logInfo("失败测试: " + std::to_string(testResults.failedTests));
    Utils::logInfo("成功率: " + std::to_string(testResults.getSuccessRate()) + "%");
    
    if (!testResults.failedTestNames.empty()) {
        Utils::logError("失败的测试:");
        for (const auto& testName : testResults.failedTestNames) {
            Utils::logError("  - " + testName);
        }
    }
}

void TestManager::resetTestResults() {
    testResults.totalTests = 0;
    testResults.passedTests = 0;
    testResults.failedTests = 0;
    testResults.failedTestNames.clear();
}

// BasicFunctionTest 实现
bool BasicFunctionTest::runAllTests() {
    Utils::logInfo("运行基础功能测试...");
    
    bool allPassed = true;
    
    // 测试时间函数
    allPassed &= testTimeFunctions();
    
    // 测试损伤类型转换
    allPassed &= testDamageTypeConversion();
    
    // 测试单位转换
    allPassed &= testUnitConversion();
    
    // 测试目录创建
    allPassed &= testDirectoryCreation();
    
    // 测试工具函数
    allPassed &= testUtilityFunctions();
    
    if (allPassed) {
        Utils::logInfo("基础功能测试: 全部通过");
    } else {
        Utils::logError("基础功能测试: 部分失败");
    }
    
    return allPassed;
}

bool BasicFunctionTest::testTimeFunctions() {
    Utils::logInfo("测试时间函数...");
    
    try {
        std::string timeStr = Utils::getCurrentTimeString();
        if (timeStr.empty()) {
            Utils::logError("时间函数返回空字符串");
            return false;
        }
        
        Utils::logInfo("当前时间: " + timeStr);
        Utils::logInfo("✓ 时间函数测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("时间函数测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testDamageTypeConversion() {
    Utils::logInfo("测试损伤类型转换...");
    
    try {
        bool allPassed = true;
        
        // 测试所有损伤类型
        for (int i = 0; i <= 7; ++i) {
            DamageType type = static_cast<DamageType>(i);
            std::string typeStr = Utils::damageTypeToString(type);
            
            if (typeStr.empty()) {
                Utils::logError("损伤类型 " + std::to_string(i) + " 转换失败");
                allPassed = false;
            } else {
                Utils::logInfo("损伤类型 " + std::to_string(i) + ": " + typeStr);
            }
        }
        
        if (allPassed) {
            Utils::logInfo("✓ 损伤类型转换测试通过");
        } else {
            Utils::logError("✗ 损伤类型转换测试失败");
        }
        
        return allPassed;
    } catch (const std::exception& e) {
        Utils::logError("损伤类型转换测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testUnitConversion() {
    Utils::logInfo("测试单位转换...");
    
    try {
        double pixels = 100.0;
        double mm = Utils::pixelToMm(pixels);
        double backToPixels = Utils::mmToPixel(mm);
        
        // 检查转换精度
        double error = std::abs(pixels - backToPixels);
        if (error > 0.001) {
            Utils::logError("单位转换精度不足，误差: " + std::to_string(error));
            return false;
        }
        
        Utils::logInfo("像素转换测试: " + std::to_string(pixels) + " pixels = " +
                       std::to_string(mm) + " mm = " + std::to_string(backToPixels) + " pixels");
        Utils::logInfo("✓ 单位转换测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("单位转换测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testDirectoryCreation() {
    Utils::logInfo("测试目录创建...");
    
    try {
        std::string testDir = "output/test_basic";
        bool success = Utils::createDirectory(testDir);
        
        if (success) {
            Utils::logInfo("✓ 测试目录创建成功: " + testDir);
            return true;
        } else {
            Utils::logError("✗ 测试目录创建失败: " + testDir);
            return false;
        }
    } catch (const std::exception& e) {
        Utils::logError("目录创建测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testUtilityFunctions() {
    Utils::logInfo("测试工具函数...");

    try {
        // 这里可以添加更多工具函数的测试
        // 目前主要测试日志功能是否正常
        Utils::logInfo("工具函数测试信息");
        Utils::logWarning("工具函数测试警告");

        Utils::logInfo("✓ 工具函数测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("工具函数测试异常: " + std::string(e.what()));
        return false;
    }
}

#ifdef USE_OPENCV
// CameraFunctionTest 实现
bool CameraFunctionTest::runAllTests() {
    Utils::logInfo("运行摄像头功能测试...");

    bool allPassed = true;

    // 测试摄像头检测
    allPassed &= testCameraDetection();

    // 如果检测成功，继续其他测试
    if (allPassed) {
        // 创建摄像头管理器进行测试
        CameraManager manager;
        if (manager.initialize() && manager.startCapture()) {
            // 测试单摄像头采集
            allPassed &= testSingleCameraCapture(manager);

            // 测试多摄像头采集（智能选择测试类型）
            if (allPassed) {
                allPassed &= testMultiCameraCapture(manager);
            }

            // 测试所有摄像头采集功能（无论单摄像头还是多摄像头）
            if (allPassed) {
                allPassed &= testAllCamerasFunction(manager);
            }

            // 测试图像质量
            if (allPassed) {
                allPassed &= testImageQuality(manager);
            }

            manager.stopCapture();
        } else {
            Utils::logError("摄像头管理器初始化失败");
            allPassed = false;
        }
    }

    if (allPassed) {
        Utils::logInfo("摄像头功能测试: 全部通过");
    } else {
        Utils::logError("摄像头功能测试: 部分失败");
    }

    return allPassed;
}

bool CameraFunctionTest::testCameraDetection() {
    Utils::logInfo("测试摄像头检测...");

    try {
        CameraManager manager;
        bool success = manager.initialize();

        if (success) {
            auto cameraInfos = manager.getAllCameraInfo();
            Utils::logInfo("检测到 " + std::to_string(cameraInfos.size()) + " 个摄像头");

            for (const auto& info : cameraInfos) {
                Utils::logInfo("摄像头 " + std::to_string(info.id) + ": " +
                              (info.status != CameraStatus::DISCONNECTED ? "可用" : "不可用"));
            }

            Utils::logInfo("✓ 摄像头检测测试通过");
            return true;
        } else {
            Utils::logError("✗ 摄像头检测失败");
            return false;
        }
    } catch (const std::exception& e) {
        Utils::logError("摄像头检测测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testSingleCameraCapture(CameraManager& manager) {
    Utils::logInfo("测试单摄像头采集...");

    try {
        // 查找第一个可用摄像头
        int availableCameraId = -1;
        auto cameraInfos = manager.getAllCameraInfo();
        for (size_t i = 0; i < cameraInfos.size(); ++i) {
            if (manager.isCameraAvailable(static_cast<int>(i))) {
                availableCameraId = static_cast<int>(i);
                break;
            }
        }

        if (availableCameraId == -1) {
            Utils::logError("没有可用的摄像头进行测试");
            return false;
        }

        Utils::logInfo("使用摄像头 " + std::to_string(availableCameraId) + " 进行测试");

        // 创建输出目录
        std::string outputDir = "output/test_camera_single";
        Utils::createDirectory(outputDir);

        cv::Mat frame;
        int frameCount = 0;
        int maxFrames = 20; // 减少测试帧数
        int failureCount = 0;
        int maxFailures = 10;

        auto startTime = std::chrono::steady_clock::now();
        auto maxTestTime = std::chrono::seconds(15); // 最大测试时间15秒

        while (frameCount < maxFrames && failureCount < maxFailures) {
            // 检查是否超时
            auto currentTime = std::chrono::steady_clock::now();
            if (currentTime - startTime > maxTestTime) {
                Utils::logWarning("单摄像头测试超时");
                break;
            }

            if (manager.getFrame(availableCameraId, frame)) {
                frameCount++;
                failureCount = 0;

                // 保存第一帧作为样本
                if (frameCount == 1) {
                    std::string filename = outputDir + "/sample_frame.jpg";
                    if (Utils::saveImage(frame, filename)) {
                        Utils::logInfo("保存样本图像: " + filename);
                    }
                }
            } else {
                failureCount++;
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        }

        auto endTime = std::chrono::steady_clock::now();
        auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            endTime - startTime).count();

        Utils::logInfo("单摄像头测试结果:");
        Utils::logInfo("  采集帧数: " + std::to_string(frameCount) + "/" + std::to_string(maxFrames));
        Utils::logInfo("  失败次数: " + std::to_string(failureCount));
        Utils::logInfo("  测试时间: " + std::to_string(totalTime) + " ms");

        bool success = frameCount > 0;
        if (success) {
            Utils::logInfo("✓ 单摄像头采集测试通过");
        } else {
            Utils::logError("✗ 单摄像头采集测试失败");
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("单摄像头采集测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testMultiCameraCapture(CameraManager& manager) {
    Utils::logInfo("测试多摄像头采集...");

    try {
        // 首先检查配置的摄像头数量
        int configuredCameras = Config::CAMERA_COUNT;
        Utils::logInfo("配置的摄像头数量: " + std::to_string(configuredCameras));

        // 检查实际可用的摄像头数量
        auto cameraInfos = manager.getAllCameraInfo();
        int availableCameras = 0;
        for (const auto& info : cameraInfos) {
            if (manager.isCameraAvailable(info.id)) {
                availableCameras++;
            }
        }
        Utils::logInfo("实际可用摄像头数量: " + std::to_string(availableCameras));

        // 如果只有一个摄像头，则跳过多摄像头测试
        if (configuredCameras <= 1 || availableCameras <= 1) {
            Utils::logWarning("检测到单摄像头环境，跳过多摄像头采集测试");
            Utils::logInfo("建议: 连接多个摄像头或修改配置文件以启用多摄像头测试");
            Utils::logInfo("✓ 多摄像头测试: 跳过（单摄像头环境）");
            return true; // 在单摄像头环境下跳过测试算作通过
        }

        std::vector<cv::Mat> frames;
        int testFrames = 10;
        int successCount = 0;
        int failureCount = 0;
        int maxFailures = 5;
        int validFrameCount = 0; // 记录有效帧数量

        auto startTime = std::chrono::steady_clock::now();
        auto maxTestTime = std::chrono::seconds(10);

        Utils::createDirectory("output/test_camera_multi");

        for (int i = 0; i < testFrames && failureCount < maxFailures; ++i) {
            auto currentTime = std::chrono::steady_clock::now();
            if (currentTime - startTime > maxTestTime) {
                Utils::logWarning("多摄像头测试超时");
                break;
            }

            if (manager.getAllFrames(frames)) {
                // 检查实际获取到的有效帧数
                int currentValidFrames = 0;
                for (size_t j = 0; j < frames.size(); ++j) {
                    if (!frames[j].empty()) {
                        currentValidFrames++;
                    }
                }

                // 只有当获取到多个有效帧时才算成功
                if (currentValidFrames >= 2) {
                    successCount++;
                    validFrameCount = currentValidFrames;
                    failureCount = 0;

                    // 保存第一组图像作为样本
                    if (i == 0) {
                        int savedCount = 0;
                        for (size_t j = 0; j < frames.size(); ++j) {
                            if (!frames[j].empty()) {
                                std::string filename = "output/test_camera_multi/camera_" +
                                                     std::to_string(j) + "_sample.jpg";
                                if (Utils::saveImage(frames[j], filename)) {
                                    savedCount++;
                                }
                            }
                        }
                        Utils::logInfo("成功保存了 " + std::to_string(savedCount) + " 个摄像头的样本图像");
                    }
                } else {
                    Utils::logWarning("获取到的有效帧数不足: " + std::to_string(currentValidFrames) +
                                     " (需要至少2个)");
                    failureCount++;
                }
            } else {
                failureCount++;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        double successRate = testFrames > 0 ? (double)successCount / testFrames * 100.0 : 0.0;

        Utils::logInfo("多摄像头测试结果:");
        Utils::logInfo("  配置摄像头数: " + std::to_string(configuredCameras));
        Utils::logInfo("  可用摄像头数: " + std::to_string(availableCameras));
        Utils::logInfo("  测试帧数: " + std::to_string(testFrames));
        Utils::logInfo("  成功次数: " + std::to_string(successCount));
        Utils::logInfo("  有效帧数: " + std::to_string(validFrameCount));
        Utils::logInfo("  成功率: " + std::to_string(successRate) + "%");

        // 提高成功率要求到80%，确保多摄像头真正工作
        bool success = successRate >= 80.0 && validFrameCount >= 2;
        if (success) {
            Utils::logInfo("✓ 多摄像头采集测试通过");
        } else {
            Utils::logError("✗ 多摄像头采集测试失败");
            if (successRate < 80.0) {
                Utils::logError("  失败原因: 成功率过低 (" + std::to_string(successRate) + "% < 80%)");
            }
            if (validFrameCount < 2) {
                Utils::logError("  失败原因: 有效摄像头数量不足 (" + std::to_string(validFrameCount) + " < 2)");
            }
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("多摄像头采集测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testImageQuality(CameraManager& manager) {
    Utils::logInfo("测试图像质量...");

    try {
        cv::Mat frame;
        auto cameraInfos = manager.getAllCameraInfo();
        bool allPassed = true;

        for (const auto& info : cameraInfos) {
            if (manager.isCameraAvailable(info.id)) {
                if (manager.getFrame(info.id, frame)) {
                    bool qualityOk = validateImageQuality(frame);
                    Utils::logInfo("摄像头 " + std::to_string(info.id) + " 图像质量: " +
                                  (qualityOk ? "合格" : "不合格"));
                    allPassed &= qualityOk;
                } else {
                    Utils::logWarning("无法获取摄像头 " + std::to_string(info.id) + " 的图像");
                    allPassed = false;
                }
            }
        }

        if (allPassed) {
            Utils::logInfo("✓ 图像质量测试通过");
        } else {
            Utils::logError("✗ 图像质量测试失败");
        }

        return allPassed;
    } catch (const std::exception& e) {
        Utils::logError("图像质量测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testAllCamerasFunction(CameraManager& manager) {
    Utils::logInfo("测试所有摄像头采集功能...");

    try {
        int configuredCameras = Config::CAMERA_COUNT;
        Utils::logInfo("测试 getAllFrames() 方法在当前环境下的行为");

        std::vector<cv::Mat> frames;
        int testFrames = 5;
        int successCount = 0;
        int totalValidFrames = 0;

        for (int i = 0; i < testFrames; ++i) {
            if (manager.getAllFrames(frames)) {
                successCount++;

                // 统计有效帧数
                int validFrames = 0;
                for (size_t j = 0; j < frames.size(); ++j) {
                    if (!frames[j].empty()) {
                        validFrames++;
                    }
                }
                totalValidFrames += validFrames;

                Utils::logInfo("第 " + std::to_string(i+1) + " 次采集: 获取到 " +
                              std::to_string(validFrames) + "/" + std::to_string(frames.size()) + " 个有效帧");
            } else {
                Utils::logWarning("第 " + std::to_string(i+1) + " 次采集失败");
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        double avgValidFrames = testFrames > 0 ? (double)totalValidFrames / testFrames : 0.0;
        double successRate = testFrames > 0 ? (double)successCount / testFrames * 100.0 : 0.0;

        Utils::logInfo("所有摄像头采集功能测试结果:");
        Utils::logInfo("  配置摄像头数: " + std::to_string(configuredCameras));
        Utils::logInfo("  测试次数: " + std::to_string(testFrames));
        Utils::logInfo("  成功次数: " + std::to_string(successCount));
        Utils::logInfo("  成功率: " + std::to_string(successRate) + "%");
        Utils::logInfo("  平均有效帧数: " + std::to_string(avgValidFrames));

        // 根据配置的摄像头数量调整期望
        bool success = false;
        if (configuredCameras == 1) {
            // 单摄像头环境：期望获取1个有效帧
            success = (successRate >= 80.0) && (avgValidFrames >= 0.8);
            Utils::logInfo("单摄像头环境评估: " + std::string(success ? "符合预期" : "不符合预期"));
        } else {
            // 多摄像头环境：期望获取多个有效帧
            success = (successRate >= 80.0) && (avgValidFrames >= configuredCameras * 0.8);
            Utils::logInfo("多摄像头环境评估: " + std::string(success ? "符合预期" : "不符合预期"));
        }

        if (success) {
            Utils::logInfo("✓ 所有摄像头采集功能测试通过");
        } else {
            Utils::logError("✗ 所有摄像头采集功能测试失败");
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("所有摄像头采集功能测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::validateImageQuality(const cv::Mat& frame) {
    if (frame.empty()) {
        return false;
    }

    // 检查图像尺寸
    if (frame.cols < 320 || frame.rows < 240) {
        Utils::logWarning("图像尺寸过小: " + std::to_string(frame.cols) + "x" + std::to_string(frame.rows));
        return false;
    }

    // 检查图像是否过暗或过亮
    cv::Scalar meanValue = cv::mean(frame);
    double brightness = meanValue[0]; // 假设是灰度图或取第一个通道

    if (brightness < 30 || brightness > 225) {
        Utils::logWarning("图像亮度异常: " + std::to_string(brightness));
        return false;
    }

    return true;
}

// SystemIntegrationTest 实现
bool SystemIntegrationTest::runAllTests() {
    Utils::logInfo("运行系统集成测试...");

    bool allPassed = true;

    // 测试系统启动和关闭
    allPassed &= testSystemStartupShutdown();

    // 其他集成测试可以在这里添加
    // allPassed &= testEndToEndFunctionality();
    // allPassed &= testPerformanceStress();
    // allPassed &= testErrorRecovery();

    if (allPassed) {
        Utils::logInfo("系统集成测试: 全部通过");
    } else {
        Utils::logError("系统集成测试: 部分失败");
    }

    return allPassed;
}

bool SystemIntegrationTest::testSystemStartupShutdown() {
    Utils::logInfo("测试系统启动和关闭...");

    try {
        // 这里可以添加系统启动和关闭的测试逻辑
        // 目前简化为基本的功能验证
        Utils::logInfo("✓ 系统启动关闭测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("系统启动关闭测试异常: " + std::string(e.what()));
        return false;
    }
}

bool SystemIntegrationTest::testEndToEndFunctionality() {
    // 端到端功能测试的实现
    return true;
}

bool SystemIntegrationTest::testPerformanceStress() {
    // 性能压力测试的实现
    return true;
}

bool SystemIntegrationTest::testErrorRecovery() {
    // 错误恢复测试的实现
    return true;
}

// ==================== 数据库功能测试实现 ====================

bool DatabaseFunctionTest::runAllTests() {
    Utils::logInfo("开始数据库功能测试...");

    bool allPassed = true;

    // 测试数据库初始化
    Utils::logInfo("测试数据库初始化...");
    allPassed &= testDatabaseInitialization();

    // 测试检测结果存储
    Utils::logInfo("测试检测结果存储...");
    allPassed &= testResultStorage();

    // 测试数据查询功能
    Utils::logInfo("测试数据查询功能...");
    allPassed &= testDataQuery();

    // 测试事务处理
    Utils::logInfo("测试事务处理...");
    allPassed &= testTransactionHandling();

    // 测试数据完整性
    Utils::logInfo("测试数据完整性...");
    allPassed &= testDataIntegrity();

    // 测试并发访问
    Utils::logInfo("测试并发访问...");
    allPassed &= testConcurrentAccess();

    // 测试数据清理功能
    Utils::logInfo("测试数据清理功能...");
    allPassed &= testDataCleanup();

    if (allPassed) {
        Utils::logInfo("✓ 所有数据库功能测试通过");
    } else {
        Utils::logError("✗ 部分数据库功能测试失败");
    }

    return allPassed;
}

bool DatabaseFunctionTest::testDatabaseInitialization() {
    try {
        // 使用临时数据库文件进行测试
        std::string testDbPath = "output/test_database.db";

        // 删除可能存在的测试数据库文件
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);

        // 测试初始化
        if (!dbManager.initialize()) {
            Utils::logError("数据库初始化失败");
            return false;
        }

        // 测试连接状态
        if (!dbManager.isConnected()) {
            Utils::logError("数据库连接状态检查失败");
            return false;
        }

        Utils::logInfo("✓ 数据库初始化测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("数据库初始化测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testResultStorage() {
    try {
        std::string testDbPath = "output/test_storage.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            Utils::logError("数据库初始化失败");
            return false;
        }

        // 创建测试数据
        auto testResults = createTestDamageResults(3);
        int testCameraId = 1;

        // 测试存储
        if (!dbManager.saveDetectionResults(testResults, testCameraId)) {
            Utils::logError("检测结果存储失败");
            return false;
        }

        // 验证存储的数据
        auto sessions = dbManager.getRecentSessions(testCameraId, 1);
        if (sessions.empty()) {
            Utils::logError("未找到存储的检测会话");
            return false;
        }

        if (sessions[0].damageCount != static_cast<int>(testResults.size())) {
            Utils::logError("存储的损伤数量不匹配");
            return false;
        }

        Utils::logInfo("✓ 检测结果存储测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("检测结果存储测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testDataQuery() {
    try {
        std::string testDbPath = "output/test_query.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            return false;
        }

        // 存储测试数据
        auto testResults = createTestDamageResults(5);
        int testCameraId = 2;

        if (!dbManager.saveDetectionResults(testResults, testCameraId)) {
            return false;
        }

        // 测试查询最近会话
        auto sessions = dbManager.getRecentSessions(testCameraId, 10);
        if (sessions.empty()) {
            Utils::logError("查询最近会话失败");
            return false;
        }

        // 测试查询损伤结果
        auto damageResults = dbManager.getDamageResults(sessions[0].id);
        if (damageResults.size() != testResults.size()) {
            Utils::logError("查询的损伤结果数量不匹配");
            return false;
        }

        // 验证数据正确性
        if (!verifyStoredData(testResults, damageResults)) {
            Utils::logError("存储数据验证失败");
            return false;
        }

        Utils::logInfo("✓ 数据查询测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("数据查询测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testTransactionHandling() {
    try {
        std::string testDbPath = "output/test_transaction.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            return false;
        }

        // 测试正常事务
        auto testResults = createTestDamageResults(2);
        if (!dbManager.saveDetectionResults(testResults, 3)) {
            Utils::logError("正常事务处理失败");
            return false;
        }

        // 验证数据已存储
        auto sessions = dbManager.getRecentSessions(3, 1);
        if (sessions.empty() || sessions[0].damageCount != 2) {
            Utils::logError("事务提交后数据验证失败");
            return false;
        }

        Utils::logInfo("✓ 事务处理测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("事务处理测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testDataIntegrity() {
    try {
        std::string testDbPath = "output/test_integrity.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            return false;
        }

        // 创建包含各种损伤类型的测试数据
        std::vector<DamageResult> testResults;

        // 创建不同类型的损伤
        DamageType types[] = {DamageType::CRACK, DamageType::WEAR, DamageType::SCRATCH,
                             DamageType::PIT, DamageType::BULGE, DamageType::AGING,
                             DamageType::INSTALL_DAMAGE};

        for (auto type : types) {
            DamageResult result;
            result.type = type;
            result.confidence = 0.8 + (static_cast<int>(type) * 0.02);
            result.size_mm = 10.0 + static_cast<int>(type);
            result.center = cv::Point2f(100.0f + static_cast<int>(type) * 10,
                                      200.0f + static_cast<int>(type) * 5);
            result.boundingBox = cv::Rect(50 + static_cast<int>(type) * 10,
                                        150 + static_cast<int>(type) * 5,
                                        20, 30);
            result.description = "测试损伤类型: " + Utils::damageTypeToString(type);
            testResults.push_back(result);
        }

        // 存储数据
        if (!dbManager.saveDetectionResults(testResults, 4)) {
            Utils::logError("数据完整性测试存储失败");
            return false;
        }

        // 查询并验证数据
        auto sessions = dbManager.getRecentSessions(4, 1);
        if (sessions.empty()) {
            Utils::logError("数据完整性测试查询会话失败");
            return false;
        }

        auto storedResults = dbManager.getDamageResults(sessions[0].id);
        if (!verifyStoredData(testResults, storedResults)) {
            Utils::logError("数据完整性验证失败");
            return false;
        }

        Utils::logInfo("✓ 数据完整性测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("数据完整性测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testConcurrentAccess() {
    try {
        std::string testDbPath = "output/test_concurrent.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            return false;
        }

        // 创建多个线程同时写入数据
        const int numThreads = 3;
        const int resultsPerThread = 2;
        std::vector<std::thread> threads;
        std::vector<bool> threadResults(numThreads, false);

        for (int i = 0; i < numThreads; ++i) {
            threads.emplace_back([&dbManager, &threadResults, i, resultsPerThread]() {
                try {
                    auto testResults = createTestDamageResults(resultsPerThread);
                    threadResults[i] = dbManager.saveDetectionResults(testResults, 5 + i);
                } catch (const std::exception& e) {
                    Utils::logError("并发访问线程 " + std::to_string(i) + " 异常: " + e.what());
                    threadResults[i] = false;
                }
            });
        }

        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }

        // 检查所有线程是否成功
        for (int i = 0; i < numThreads; ++i) {
            if (!threadResults[i]) {
                Utils::logError("并发访问线程 " + std::to_string(i) + " 失败");
                return false;
            }
        }

        // 验证数据总数
        int totalExpectedSessions = numThreads;
        int actualSessions = 0;
        for (int i = 0; i < numThreads; ++i) {
            auto sessions = dbManager.getRecentSessions(5 + i, 10);
            actualSessions += sessions.size();
        }

        if (actualSessions != totalExpectedSessions) {
            Utils::logError("并发访问后会话数量不匹配，期望: " + std::to_string(totalExpectedSessions) +
                          "，实际: " + std::to_string(actualSessions));
            return false;
        }

        Utils::logInfo("✓ 并发访问测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("并发访问测试异常: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseFunctionTest::testDataCleanup() {
    try {
        std::string testDbPath = "output/test_cleanup.db";
        std::remove(testDbPath.c_str());

        DatabaseManager dbManager(testDbPath);
        if (!dbManager.initialize()) {
            return false;
        }

        // 存储一些测试数据
        auto testResults = createTestDamageResults(3);
        if (!dbManager.saveDetectionResults(testResults, 8)) {
            return false;
        }

        // 测试清理功能（保留-1天，强制清理所有数据）
        if (!dbManager.cleanupOldData(-1)) {
            Utils::logError("数据清理操作失败");
            return false;
        }

        // 验证数据已被清理
        auto sessions = dbManager.getRecentSessions(8, 10);
        if (!sessions.empty()) {
            Utils::logError("数据清理后仍有残留数据");
            return false;
        }

        Utils::logInfo("✓ 数据清理测试通过");
        return true;

    } catch (const std::exception& e) {
        Utils::logError("数据清理测试异常: " + std::string(e.what()));
        return false;
    }
}

std::vector<DamageResult> DatabaseFunctionTest::createTestDamageResults(int count) {
    std::vector<DamageResult> results;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> confidenceDist(0.7, 0.95);
    std::uniform_real_distribution<> sizeDist(5.0, 50.0);
    std::uniform_int_distribution<> posDist(10, 500);

    DamageType types[] = {DamageType::CRACK, DamageType::WEAR, DamageType::SCRATCH};

    for (int i = 0; i < count; ++i) {
        DamageResult result;
        result.type = types[i % 3];
        result.confidence = confidenceDist(gen);
        result.size_mm = sizeDist(gen);
        result.center = cv::Point2f(static_cast<float>(posDist(gen)),
                                  static_cast<float>(posDist(gen)));
        result.boundingBox = cv::Rect(posDist(gen), posDist(gen),
                                    20 + i * 5, 30 + i * 3);
        result.description = "测试损伤 " + std::to_string(i + 1) +
                           " - " + Utils::damageTypeToString(result.type);
        result.timestamp = std::chrono::system_clock::now();

        results.push_back(result);
    }

    return results;
}

bool DatabaseFunctionTest::verifyStoredData(const std::vector<DamageResult>& original,
                                          const std::vector<DatabaseManager::DatabaseDamageResult>& stored) {
    if (original.size() != stored.size()) {
        Utils::logError("数据数量不匹配，原始: " + std::to_string(original.size()) +
                       "，存储: " + std::to_string(stored.size()));
        return false;
    }

    for (size_t i = 0; i < original.size(); ++i) {
        const auto& orig = original[i];
        const auto& stor = stored[i];

        // 验证损伤类型（DatabaseManager使用英文类型名）
        std::string expectedType;
        switch (orig.type) {
            case DamageType::CRACK: expectedType = "CRACK"; break;
            case DamageType::WEAR: expectedType = "WEAR"; break;
            case DamageType::SCRATCH: expectedType = "SCRATCH"; break;
            case DamageType::PIT: expectedType = "PIT"; break;
            case DamageType::BULGE: expectedType = "BULGE"; break;
            case DamageType::AGING: expectedType = "AGING"; break;
            case DamageType::INSTALL_DAMAGE: expectedType = "INSTALL_DAMAGE"; break;
            default: expectedType = "NONE"; break;
        }
        if (stor.damageType != expectedType) {
            Utils::logError("损伤类型不匹配，期望: " + expectedType +
                          "，实际: " + stor.damageType);
            return false;
        }

        // 验证置信度（允许小的浮点误差）
        if (std::abs(stor.confidence - orig.confidence) > 0.001) {
            Utils::logError("置信度不匹配，期望: " + std::to_string(orig.confidence) +
                          "，实际: " + std::to_string(stor.confidence));
            return false;
        }

        // 验证尺寸
        if (std::abs(stor.sizeMm - orig.size_mm) > 0.001) {
            Utils::logError("尺寸不匹配，期望: " + std::to_string(orig.size_mm) +
                          "，实际: " + std::to_string(stor.sizeMm));
            return false;
        }

        // 验证中心点
        if (std::abs(stor.centerX - orig.center.x) > 0.001 ||
            std::abs(stor.centerY - orig.center.y) > 0.001) {
            Utils::logError("中心点不匹配");
            return false;
        }

        // 验证边界框
        if (stor.bboxX != orig.boundingBox.x || stor.bboxY != orig.boundingBox.y ||
            stor.bboxWidth != orig.boundingBox.width || stor.bboxHeight != orig.boundingBox.height) {
            Utils::logError("边界框不匹配");
            return false;
        }

        // 验证描述
        if (stor.description != orig.description) {
            Utils::logError("描述不匹配，期望: " + orig.description +
                          "，实际: " + stor.description);
            return false;
        }
    }

    return true;
}

#endif // USE_OPENCV
