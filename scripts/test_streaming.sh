#!/bin/bash

# 视频推流功能集成测试脚本
# 用于验证FFmpeg推流功能是否正常工作

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查FFmpeg
    if ! command -v ffmpeg &> /dev/null; then
        log_error "FFmpeg未安装，请先安装FFmpeg"
        echo "Ubuntu/Debian: sudo apt-get install ffmpeg"
        echo "CentOS/RHEL: sudo yum install ffmpeg"
        exit 1
    fi
    log_success "FFmpeg已安装: $(ffmpeg -version | head -n1)"
    
    # 检查VLC（可选）
    if command -v vlc &> /dev/null; then
        log_success "VLC已安装: $(vlc --version | head -n1)"
    else
        log_warning "VLC未安装，无法进行播放测试"
        log_info "安装VLC: sudo apt-get install vlc"
    fi
    
    # 检查MediaMTX（如果存在）
    if [ -f "./mediamtx" ]; then
        log_success "发现MediaMTX服务器"
    else
        log_warning "未发现MediaMTX服务器，将尝试下载"
        download_mediamtx
    fi
}

# 下载MediaMTX
download_mediamtx() {
    log_info "下载MediaMTX RTSP服务器..."
    
    # 检测系统架构
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            ARCH_SUFFIX="amd64"
            ;;
        aarch64)
            ARCH_SUFFIX="arm64v8"
            ;;
        armv7l)
            ARCH_SUFFIX="armv7"
            ;;
        *)
            log_error "不支持的架构: $ARCH"
            exit 1
            ;;
    esac
    
    # 下载最新版本
    DOWNLOAD_URL="https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_linux_${ARCH_SUFFIX}.tar.gz"
    
    log_info "下载地址: $DOWNLOAD_URL"
    
    if wget -q "$DOWNLOAD_URL" -O mediamtx.tar.gz; then
        tar -xzf mediamtx.tar.gz
        chmod +x mediamtx
        rm mediamtx.tar.gz
        log_success "MediaMTX下载完成"
    else
        log_error "MediaMTX下载失败"
        exit 1
    fi
}

# 启动MediaMTX服务器
start_mediamtx() {
    log_info "启动MediaMTX RTSP服务器..."
    
    # 检查端口是否被占用
    if netstat -tuln | grep -q ":8554 "; then
        log_warning "端口8554已被占用，尝试停止现有服务"
        pkill -f mediamtx || true
        sleep 2
    fi
    
    # 启动MediaMTX
    ./mediamtx &
    MEDIAMTX_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否启动成功
    if kill -0 $MEDIAMTX_PID 2>/dev/null; then
        log_success "MediaMTX服务器启动成功 (PID: $MEDIAMTX_PID)"
        echo $MEDIAMTX_PID > mediamtx.pid
    else
        log_error "MediaMTX服务器启动失败"
        exit 1
    fi
}

# 停止MediaMTX服务器
stop_mediamtx() {
    if [ -f "mediamtx.pid" ]; then
        PID=$(cat mediamtx.pid)
        if kill -0 $PID 2>/dev/null; then
            log_info "停止MediaMTX服务器 (PID: $PID)"
            kill $PID
            rm mediamtx.pid
        fi
    fi
}

# 编译项目
build_project() {
    log_info "编译缺陷检测系统..."
    
    cd build
    
    if make -j$(nproc); then
        log_success "项目编译成功"
    else
        log_error "项目编译失败"
        exit 1
    fi
    
    cd ..
}

# 启用推流配置
enable_streaming() {
    log_info "启用推流配置..."
    
    # 备份原配置
    cp build/config/system_config.json build/config/system_config.json.bak
    
    # 修改配置启用推流
    sed -i 's/"enabled": false/"enabled": true/' build/config/system_config.json
    
    log_success "推流配置已启用"
}

# 恢复配置
restore_config() {
    if [ -f "build/config/system_config.json.bak" ]; then
        log_info "恢复原始配置..."
        mv build/config/system_config.json.bak build/config/system_config.json
    fi
}

# 运行推流测试
run_streaming_test() {
    log_info "运行推流功能测试..."
    
    cd build
    
    # 运行推流测试
    timeout 30s ./bin/FaultDetectRefactored --test streaming || {
        log_warning "推流测试超时或失败，这可能是正常的"
    }
    
    cd ..
}

# 运行系统集成测试
run_integration_test() {
    log_info "运行系统集成测试..."
    
    cd build
    
    # 启动系统（后台运行）
    timeout 10s ./bin/FaultDetectRefactored &
    SYSTEM_PID=$!
    
    # 等待系统启动
    sleep 5
    
    # 检查推流是否工作
    log_info "检查RTSP流是否可用..."
    
    # 使用ffprobe检查流
    if timeout 5s ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height,r_frame_rate -of csv=p=0 rtsp://localhost:8554/live; then
        log_success "RTSP流检测成功"
    else
        log_warning "RTSP流检测失败，可能需要更长时间启动"
    fi
    
    # 停止系统
    if kill -0 $SYSTEM_PID 2>/dev/null; then
        kill $SYSTEM_PID
        wait $SYSTEM_PID 2>/dev/null || true
    fi
    
    cd ..
}

# VLC播放测试
test_vlc_playback() {
    if command -v vlc &> /dev/null; then
        log_info "启动VLC播放测试..."
        log_info "请在VLC中打开网络流: rtsp://localhost:8554/live"
        log_info "按任意键继续..."
        read -n 1 -s
        
        # 启动VLC（后台）
        vlc rtsp://localhost:8554/live &
        VLC_PID=$!
        
        log_info "VLC已启动，请检查视频播放是否正常"
        log_info "按任意键停止VLC..."
        read -n 1 -s
        
        # 停止VLC
        kill $VLC_PID 2>/dev/null || true
    else
        log_warning "VLC未安装，跳过播放测试"
    fi
}

# 性能测试
performance_test() {
    log_info "运行性能测试..."
    
    cd build
    
    # 运行系统并监控性能
    log_info "启动系统进行性能监控..."
    
    # 使用top命令监控CPU和内存使用
    timeout 30s ./bin/FaultDetectRefactored &
    SYSTEM_PID=$!
    
    # 监控资源使用
    log_info "监控系统资源使用情况..."
    for i in {1..10}; do
        if kill -0 $SYSTEM_PID 2>/dev/null; then
            CPU_MEM=$(ps -p $SYSTEM_PID -o %cpu,%mem --no-headers)
            log_info "资源使用 ($i/10): CPU=${CPU_MEM% *}%, MEM=${CPU_MEM#* }%"
            sleep 3
        else
            break
        fi
    done
    
    # 停止系统
    if kill -0 $SYSTEM_PID 2>/dev/null; then
        kill $SYSTEM_PID
        wait $SYSTEM_PID 2>/dev/null || true
    fi
    
    cd ..
    
    log_success "性能测试完成"
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 停止MediaMTX
    stop_mediamtx
    
    # 恢复配置
    restore_config
    
    # 停止可能残留的进程
    pkill -f FaultDetectRefactored || true
    pkill -f vlc || true
    
    log_success "清理完成"
}

# 主函数
main() {
    log_info "=== 视频推流功能集成测试 ==="
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 检查依赖
    check_dependencies
    
    # 编译项目
    build_project
    
    # 启动MediaMTX
    start_mediamtx
    
    # 启用推流配置
    enable_streaming
    
    # 运行推流测试
    run_streaming_test
    
    # 运行集成测试
    run_integration_test
    
    # VLC播放测试
    test_vlc_playback
    
    # 性能测试
    performance_test
    
    log_success "=== 所有测试完成 ==="
    log_info "推流地址: rtsp://localhost:8554/live"
    log_info "可以使用VLC或其他播放器测试推流功能"
}

# 运行主函数
main "$@"
